<?php $__env->startSection('title', $title); ?>
<?php $__env->startSection('content'); ?>
    <style>
        #tabelMasterCampaign {
            text-align: center;
        }

        .action-container {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.25);
        }

        .stats-item {
            text-align: center;
            position: relative;
        }

        .stats-number {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stats-label {
            font-size: 0.95rem;
            opacity: 0.95;
            font-weight: 500;
        }

        .stats-change {
            font-size: 0.75rem;
            margin-top: 5px;
            opacity: 0.8;
        }

        .stats-change.positive {
            color: #4ade80;
        }

        .stats-change.negative {
            color: #f87171;
        }

        .auto-refresh-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #28a745;
            background: rgba(40, 167, 69, 0.1);
            padding: 8px 12px;
            border-radius: 20px;
            border: 1px solid rgba(40, 167, 69, 0.2);
            transition: all 0.3s ease;
        }

        .auto-refresh-indicator.disabled {
            color: #6c757d;
            background: rgba(108, 117, 125, 0.1);
            border-color: rgba(108, 117, 125, 0.2);
        }

        .refresh-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #28a745;
            animation: pulse 2s infinite;
        }

        .refresh-dot.disabled {
            background-color: #6c757d;
            animation: none;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
                transform: scale(1);
            }

            50% {
                opacity: 0.6;
                transform: scale(1.1);
            }

            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .filter-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .btn-group-toggle .btn {
            margin-right: 5px;
        }

        .last-updated {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .sync-status {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .sync-status.syncing {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.2);
        }

        .sync-status.success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .sync-status.error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.2);
        }

        .notification-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border-left: 4px solid #28a745;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification-toast.show {
            transform: translateX(0);
        }

        .notification-toast.error {
            border-left-color: #dc3545;
        }

        .notification-toast.warning {
            border-left-color: #ffc107;
        }

        .btn-modern {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }



        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: 12px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h4 class="mb-1 text-dark fw-bold"><?php echo e($title); ?></h4>
            <p class="text-muted mb-0">Monitor performa kampanye Facebook Ads secara real-time</p>
        </div>
        <div class="d-flex align-items-center gap-3">
            <div class="auto-refresh-indicator" id="autoRefreshIndicator">
                <span class="refresh-dot" id="refreshDot"></span>
                <span id="refreshStatus">Auto-refresh: ON</span>
            </div>
            <div class="sync-status success" id="syncStatus">
                <i class="bx bx-check-circle"></i>
                <span>Tersinkronisasi</span>
            </div>
        </div>
    </div>



    <div class="card table-modern">
        <div class="card-body position-relative">
            <!-- Loading Overlay for Table -->
            <div class="loading-overlay d-none" id="tableLoading">
                <div class="loading-spinner"></div>
            </div>

            <!-- Enhanced Filter Section -->
            <div class="filter-section">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <label class="form-label fw-semibold">
                            <i class="bx bx-user me-1"></i>Akun Iklan
                        </label>
                        <select class="form-select" id="idAkun" name="idAkun">
                            <option value="">Pilih Akun Iklan</option>
                            <?php $__currentLoopData = $akun; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $a): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($a['id']); ?>"><?php echo e($a['nama']); ?> (<?php echo e($a['id']); ?>)</option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-semibold">
                            <i class="bx bx-calendar me-1"></i>Rentang Tanggal
                        </label>
                        <input type="text" id="dateRangePicker" class="form-control"
                               value="<?php echo e(date('Y-m-01') . ' to ' . date('Y-m-t')); ?>"
                               placeholder="Pilih Rentang Tanggal">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label fw-semibold">
                            <i class="bx bx-filter me-1"></i>Status
                        </label>
                        <select class="form-select" id="statusFilter">
                            <option value="">Semua Status</option>
                            <option value="ACTIVE">Aktif</option>
                            <option value="PAUSED">Dijeda</option>
                            <option value="ARCHIVED">Diarsipkan</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label fw-semibold">
                            <i class="bx bx-time me-1"></i>Interval Refresh
                        </label>
                        <select class="form-select" id="refreshInterval">
                            <option value="10">10 detik</option>
                            <option value="30" selected>30 detik</option>
                            <option value="60">1 menit</option>
                            <option value="300">5 menit</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label fw-semibold">Aksi</label>
                        <div class="d-flex gap-1">
                            <button id="btnRefreshData" class="btn btn-success btn-sm btn-modern" title="Refresh Data">
                                <i class="bx bx-refresh"></i>
                            </button>
                            <button id="btnToggleAutoRefresh" class="btn btn-outline-primary btn-sm btn-modern" title="Toggle Auto Refresh">
                                <i class="bx bx-time"></i>
                            </button>
                            <button id="btnSyncData" class="btn btn-primary btn-sm btn-modern" title="Sinkronisasi Data">
                                <i class="bx bx-sync"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="last-updated">
                            <i class="bx bx-time-five"></i>
                            Terakhir diperbarui: <span id="lastUpdated">-</span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-end gap-2">
                            <span class="small text-muted">Refresh otomatis dalam:</span>
                            <span class="badge bg-primary" id="countdownTimer">30s</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table id="tabelMasterCampaign" class="table table-hover" style="width:100%">
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th width="30%">Kampanye</th>
                            <th width="10%">Status</th>
                            <th width="15%">Hasil</th>
                            <th width="20%">CPR</th>
                            <th width="20%">Total Pengeluaran</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
    <div id="modalContainer"></div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.js"></script>
    <script>
        $(document).ready(function() {
            // Global variables
            let autoRefreshEnabled = true;
            let autoRefreshInterval = null;
            let countdownInterval = null;
            let lastUpdateTime = null;
            let refreshIntervalSeconds = 30;
            let countdownSeconds = refreshIntervalSeconds;
            let previousStats = {};
            let isFirstLoad = true;

            // Initialize flatpickr
            flatpickr("#dateRangePicker", {
                mode: "range",
                dateFormat: "Y-m-d",
                locale: {
                    rangeSeparator: " to "
                }
            });

            var date = $("#dateRangePicker").val();
            var idAkun = $('#idAkun option:selected').val();
            var statusFilter = $('#statusFilter').val();

            // Enhanced Helper functions
            const limitText = (text, limit = 25) => {
                if (!text) return '-';
                return text.length > limit ? text.substring(0, limit) + '...' : text;
            };

            const formatCurrency = (value) => {
                if (!value || value === 0) return '-';
                let pesoValue = parseFloat(value);
                let rupiah = pesoValue * 278.62;
                return 'Rp ' + rupiah.toLocaleString('id-ID', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                });
            };

            const formatNumber = (value) => {
                if (!value || value === 0) return '0';
                return parseInt(value).toLocaleString('id-ID');
            };

            const updateLastUpdatedTime = () => {
                lastUpdateTime = new Date();
                $('#lastUpdated').text(lastUpdateTime.toLocaleString('id-ID', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                }));
            };

            const showLoading = (show = true, target = 'buttons') => {
                if (target === 'buttons') {
                    if (show) {
                        $('#btnRefreshData i').removeClass('bx-refresh').addClass('bx-loader-alt bx-spin');
                        $('#btnSyncData i').removeClass('bx-sync').addClass('bx-loader-alt bx-spin');
                    } else {
                        $('#btnRefreshData i').removeClass('bx-loader-alt bx-spin').addClass('bx-refresh');
                        $('#btnSyncData i').removeClass('bx-loader-alt bx-spin').addClass('bx-sync');
                    }
                } else if (target === 'table') {
                    $('#tableLoading').toggleClass('d-none', !show);
                } else if (target === 'stats') {
                    $('#statsLoading').toggleClass('d-none', !show);
                }
            };

            const showNotification = (title, message, type = 'success') => {
                // Map type to SweetAlert2 icon
                let icon = 'success';
                switch (type) {
                    case 'error':
                        icon = 'error';
                        break;
                    case 'warning':
                        icon = 'warning';
                        break;
                    case 'info':
                        icon = 'info';
                        break;
                    default:
                        icon = 'success';
                }

                // Create SweetAlert2 toast
                const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.addEventListener('mouseenter', Swal.stopTimer)
                        toast.addEventListener('mouseleave', Swal.resumeTimer)
                    }
                });

                Toast.fire({
                    icon: icon,
                    title: title,
                    text: message
                });
            };

            const updateSyncStatus = (status, message) => {
                const syncStatus = $('#syncStatus');
                const icon = syncStatus.find('i');
                const text = syncStatus.find('span');

                // Reset classes
                syncStatus.removeClass('syncing success error');
                icon.removeClass('bx-loader-alt bx-spin bx-check-circle bx-error-circle');

                // Set new status
                syncStatus.addClass(status);
                text.text(message);

                switch (status) {
                    case 'syncing':
                        icon.addClass('bx-loader-alt bx-spin');
                        break;
                    case 'success':
                        icon.addClass('bx-check-circle');
                        break;
                    case 'error':
                        icon.addClass('bx-error-circle');
                        break;
                }
            };

            const updateStatistics = (data) => {
                let totalCampaigns = data.length;
                let activeCampaigns = data.filter(item => item.status === 'ACTIVE').length;
                let totalSpend = data.reduce((sum, item) => sum + parseFloat(item.biaya_dibelanjakan || 0), 0);
                let totalResults = data.reduce((sum, item) => sum + parseInt(item.hasil || 0), 0);
                let avgCpr = totalResults > 0 ? totalSpend / totalResults : 0;

                // Calculate changes from previous values
                const currentStats = {
                    totalCampaigns,
                    activeCampaigns,
                    totalSpend,
                    avgCpr
                };

                // Update main numbers with animation
                animateNumber('#totalCampaigns', formatNumber(totalCampaigns));
                animateNumber('#activeCampaigns', formatNumber(activeCampaigns));
                animateNumber('#totalSpend', formatCurrency(totalSpend));
                animateNumber('#avgCpr', formatCurrency(avgCpr));

                // Show changes if not first load
                if (!isFirstLoad && Object.keys(previousStats).length > 0) {
                    updateStatChange('#totalCampaignsChange', currentStats.totalCampaigns, previousStats.totalCampaigns);
                    updateStatChange('#activeCampaignsChange', currentStats.activeCampaigns, previousStats.activeCampaigns);
                    updateStatChange('#totalSpendChange', currentStats.totalSpend, previousStats.totalSpend, true);
                    updateStatChange('#avgCprChange', currentStats.avgCpr, previousStats.avgCpr, true);
                }

                // Store current stats for next comparison
                previousStats = currentStats;
                isFirstLoad = false;
            };

            const animateNumber = (selector, newValue) => {
                const element = $(selector);
                element.fadeOut(200, function() {
                    element.text(newValue).fadeIn(200);
                });
            };

            const updateStatChange = (selector, current, previous, isCurrency = false) => {
                const element = $(selector);
                const change = current - previous;
                const percentage = previous !== 0 ? ((change / previous) * 100).toFixed(1) : 0;

                if (change === 0) {
                    element.text('').removeClass('positive negative');
                    return;
                }

                const isPositive = change > 0;
                const changeText = isCurrency ?
                    `${isPositive ? '+' : ''}${formatCurrency(Math.abs(change))} (${isPositive ? '+' : ''}${percentage}%)` :
                    `${isPositive ? '+' : ''}${formatNumber(Math.abs(change))} (${isPositive ? '+' : ''}${percentage}%)`;

                element.text(changeText)
                    .removeClass('positive negative')
                    .addClass(isPositive ? 'positive' : 'negative');
            };

            // Countdown timer functions
            const startCountdown = () => {
                if (countdownInterval) clearInterval(countdownInterval);

                countdownInterval = setInterval(() => {
                    if (autoRefreshEnabled && countdownSeconds > 0) {
                        countdownSeconds--;
                        updateCountdownDisplay();
                    } else if (autoRefreshEnabled && countdownSeconds <= 0) {
                        // Reset countdown and refresh
                        countdownSeconds = refreshIntervalSeconds;
                        refreshData();
                    }
                }, 1000);
            };

            const updateCountdownDisplay = () => {
                const minutes = Math.floor(countdownSeconds / 60);
                const seconds = countdownSeconds % 60;
                const display = minutes > 0 ?
                    `${minutes}m ${seconds}s` :
                    `${seconds}s`;
                $('#countdownTimer').text(display);
            };

            const resetCountdown = () => {
                countdownSeconds = refreshIntervalSeconds;
                updateCountdownDisplay();
            };

            // Enhanced refresh data function
            const refreshData = () => {
                showLoading(true, 'table');
                updateSyncStatus('syncing', 'Memperbarui data...');
                table.draw();
                resetCountdown();
            };

            // Initialize DataTable with enhanced features
            var table = $('#tabelMasterCampaign').DataTable({
                ajax: {
                    url: "<?php echo e(route('ads.monitoring-cpr.data')); ?>",
                    data: function(d) {
                        d.date = date;
                        d.idAkun = idAkun;
                        d.status = statusFilter;
                    },
                    dataSrc: function(json) {
                        try {
                            updateStatistics(json.data);
                            updateLastUpdatedTime();
                            updateSyncStatus('success', 'Tersinkronisasi');

                            if (!isFirstLoad) {
                                showNotification(
                                    'Data Diperbarui',
                                    `${json.data.length} kampanye berhasil dimuat`,
                                    'success'
                                );
                            }

                            return json.data;
                        } catch (error) {
                            console.error('Error processing data:', error);
                            updateSyncStatus('error', 'Gagal memproses data');
                            showNotification(
                                'Error',
                                'Gagal memproses data dari server',
                                'error'
                            );
                            return [];
                        } finally {
                            showLoading(false, 'table');
                            showLoading(false, 'buttons');
                        }
                    },
                    error: function(xhr, error, thrown) {
                        console.error('AJAX Error:', error);
                        updateSyncStatus('error', 'Koneksi bermasalah');
                        showNotification(
                            'Koneksi Error',
                            'Gagal mengambil data dari server',
                            'error'
                        );
                        showLoading(false, 'table');
                        showLoading(false, 'buttons');
                    }
                },
                processing: true,
                serverSide: true,
                pageLength: 25,
                lengthMenu: [
                    [10, 25, 50, 100],
                    [10, 25, 50, 100]
                ],
                order: [
                    [1, 'desc']
                ],
                language: {
                    processing: '<div class="d-flex align-items-center"><div class="loading-spinner me-2"></div>Memuat data...</div>',
                    search: 'Cari:',
                    lengthMenu: 'Tampilkan _MENU_ data per halaman',
                    info: 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
                    infoEmpty: 'Tidak ada data tersedia',
                    infoFiltered: '(difilter dari _MAX_ total data)',
                    paginate: {
                        first: 'Pertama',
                        last: 'Terakhir',
                        next: 'Selanjutnya',
                        previous: 'Sebelumnya'
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false,
                        width: '5%',
                        className: 'text-center'
                    },
                    {
                        data: 'nama_kampanye',
                        render: data => {
                            if (!data) return '-';
                            const truncated = limitText(data, 40);
                            return `<div style="text-align:left;" title="${data}">
                                        <strong>${truncated}</strong>
                                    </div>`;
                        },
                        width: '30%'
                    },
                    {
                        data: 'status',
                        render: data => {
                            if (!data) return '-';
                            let badgeClass = 'secondary';
                            let icon = 'bx-help-circle';

                            switch (data) {
                                case 'ACTIVE':
                                    badgeClass = 'success';
                                    icon = 'bx-play-circle';
                                    break;
                                case 'PAUSED':
                                    badgeClass = 'warning';
                                    icon = 'bx-pause-circle';
                                    break;
                                case 'ARCHIVED':
                                    badgeClass = 'danger';
                                    icon = 'bx-archive';
                                    break;
                            }
                            return `<span class="badge bg-${badgeClass}">
                                        <i class="bx ${icon} me-1"></i>${data}
                                    </span>`;
                        },
                        width: '10%',
                        className: 'text-center'
                    },
                    {
                        data: 'hasil',
                        render: data => {
                            const formatted = formatNumber(data);
                            return `<span class="fw-semibold">${formatted}</span>`;
                        },
                        width: '15%',
                        className: 'text-center'
                    },
                    {
                        data: 'cpr',
                        render: data => {
                            let formatted = formatCurrency(data);
                            let value = parseFloat(data);
                            let colorClass = 'text-dark';
                            let icon = '';

                            // Enhanced color coding based on CPR value
                            if (value > 50000) {
                                colorClass = 'text-danger';
                                icon = '<i class="bx bx-trending-up me-1"></i>';
                            } else if (value > 25000) {
                                colorClass = 'text-warning';
                                icon = '<i class="bx bx-minus me-1"></i>';
                            } else if (value > 0) {
                                colorClass = 'text-success';
                                icon = '<i class="bx bx-trending-down me-1"></i>';
                            }

                            return `<span class="${colorClass} fw-semibold">
                                        ${icon}${formatted}
                                    </span>`;
                        },
                        width: '20%',
                        className: 'text-center'
                    },
                    {
                        data: 'biaya_dibelanjakan',
                        render: data => {
                            const formatted = formatCurrency(data);
                            return `<span class="fw-semibold">${formatted}</span>`;
                        },
                        width: '20%',
                        className: 'text-center'
                    },
                ]
            });

            // Enhanced Auto-refresh functionality
            const startAutoRefresh = () => {
                if (autoRefreshInterval) clearInterval(autoRefreshInterval);

                // Start countdown timer
                startCountdown();

                // Note: The actual refresh is handled by the countdown timer
                // This ensures better synchronization between countdown and refresh
            };

            const stopAutoRefresh = () => {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                    countdownInterval = null;
                }
            };

            const toggleAutoRefresh = () => {
                autoRefreshEnabled = !autoRefreshEnabled;
                const indicator = $('#autoRefreshIndicator');
                const dot = $('#refreshDot');
                const status = $('#refreshStatus');
                const countdownTimer = $('#countdownTimer');

                if (autoRefreshEnabled) {
                    indicator.removeClass('disabled');
                    dot.removeClass('disabled');
                    status.text('Auto-refresh: ON');
                    countdownTimer.removeClass('d-none');
                    resetCountdown();
                    startAutoRefresh();

                    showNotification(
                        'Auto-refresh Diaktifkan',
                        `Data akan diperbarui setiap ${refreshIntervalSeconds} detik`,
                        'success'
                    );
                } else {
                    indicator.addClass('disabled');
                    dot.addClass('disabled');
                    status.text('Auto-refresh: OFF');
                    countdownTimer.addClass('d-none');
                    stopAutoRefresh();

                    showNotification(
                        'Auto-refresh Dinonaktifkan',
                        'Refresh otomatis telah dihentikan',
                        'warning'
                    );
                }
            };

            // Update refresh interval
            const updateRefreshInterval = (newInterval) => {
                refreshIntervalSeconds = parseInt(newInterval);
                countdownSeconds = refreshIntervalSeconds;

                if (autoRefreshEnabled) {
                    stopAutoRefresh();
                    startAutoRefresh();

                    showNotification(
                        'Interval Diperbarui',
                        `Auto-refresh sekarang setiap ${refreshIntervalSeconds} detik`,
                        'success'
                    );
                }

                updateCountdownDisplay();
            };

            // Enhanced Event handlers
            $('#dateRangePicker').change(function() {
                if ($(this).val().split(' to ').length === 2) {
                    date = $(this).val();
                    showLoading(true, 'table');
                    updateSyncStatus('syncing', 'Memfilter data...');
                    refreshData();
                }
            });

            $('#idAkun').change(function() {
                idAkun = $(this).val();
                const selectedText = $(this).find('option:selected').text();
                showLoading(true, 'table');
                updateSyncStatus('syncing', 'Mengubah akun...');
                refreshData();

                if (idAkun) {
                    showNotification(
                        'Akun Diubah',
                        `Menampilkan data untuk: ${selectedText}`,
                        'success'
                    );
                }
            });

            $('#statusFilter').change(function() {
                statusFilter = $(this).val();
                const statusText = $(this).find('option:selected').text();
                showLoading(true, 'table');
                updateSyncStatus('syncing', 'Memfilter status...');
                refreshData();

                showNotification(
                    'Filter Status',
                    `Menampilkan kampanye: ${statusText}`,
                    'success'
                );
            });

            $('#refreshInterval').change(function() {
                const newInterval = $(this).val();
                updateRefreshInterval(newInterval);
            });

            $('#btnRefreshData').click(function() {
                showLoading(true, 'buttons');
                refreshData();

                showNotification(
                    'Refresh Manual',
                    'Data sedang diperbarui...',
                    'success'
                );
            });

            $('#btnToggleAutoRefresh').click(function() {
                toggleAutoRefresh();
            });

            $('#btnSyncData').click(function() {
                var selectedIdAkun = $('#idAkun').val();
                var namaAkun = $('#idAkun option:selected').text();

                if (!selectedIdAkun) {
                    showNotification(
                        'Pilih Akun',
                        'Silakan pilih akun iklan terlebih dahulu untuk sinkronisasi',
                        'warning'
                    );
                    return;
                }

                showLoading(true, 'buttons');
                updateSyncStatus('syncing', 'Sinkronisasi dengan Facebook...');

                $.get("<?php echo e(route('ads.monitoring-cpr.create')); ?>", {
                    idAkun: selectedIdAkun,
                    namaAkun: namaAkun
                }, function(data) {
                    $('#modalContainer').html(data);
                    $('#modalCreateCampaign').modal('show');
                    showLoading(false, 'buttons');
                    updateSyncStatus('success', 'Tersinkronisasi');

                    showNotification(
                        'Sinkronisasi Berhasil',
                        `Data kampanye dari ${namaAkun} berhasil dimuat`,
                        'success'
                    );

                    $(document).off('click', '#btnSimpanCampaign').on('click', '#btnSimpanCampaign', function() {
                        simpanData(
                            '#formCampaign',
                            '#modalCreateCampaign',
                            "<?php echo e(route('ads.monitoring-cpr.store')); ?>",
                            table
                        );
                    });
                }).fail(function(xhr, status, error) {
                    showLoading(false, 'buttons');
                    updateSyncStatus('error', 'Gagal sinkronisasi');

                    let errorMessage = 'Gagal mengambil data dari Facebook Ads';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    showNotification(
                        'Sinkronisasi Gagal',
                        errorMessage,
                        'error'
                    );
                });
            });

            // Initialize enhanced monitoring
            const initializeMonitoring = () => {
                // Set initial countdown display
                updateCountdownDisplay();

                // Start auto-refresh
                startAutoRefresh();

                // Update last updated time
                updateLastUpdatedTime();

                // Show welcome notification
                setTimeout(() => {
                    showNotification(
                        'Monitoring CPR Aktif',
                        `Auto-refresh setiap ${refreshIntervalSeconds} detik. Selamat memantau!`,
                        'success'
                    );
                }, 1000);
            };

            // Initialize the monitoring system
            initializeMonitoring();

            // Cleanup on page unload
            $(window).on('beforeunload', function() {
                stopAutoRefresh();
            });
        });

        // Event: Klik tombol Edit
        $('#tabelCampaign').on('click', '.btn-edit', function() {
            var id = $(this).data('id');
            var url = "<?php echo e(route('ads.monitoring-cpr.edit', ':id')); ?>".replace(':id', id);
            $.get(url, function(data) {
                $('#modalContainer').html(data);
                $('#modalCreateCampaign').modal('show');

                // === Gunakan Fungsi Reusable untuk Simpan Data ===
                $(document).off('click', '#btnSimpanCampaign').on('click', '#btnSimpanCampaign', function() {
                    simpanData(
                        '#formCampaign',
                        '#modalCreateCampaign',
                        "<?php echo e(route('ads.monitoring-cpr.update', ':id')); ?>".replace(':id', id),
                        table
                    );
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('templates.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\jualinn\resources\views/monitoring-cpr/index.blade.php ENDPATH**/ ?>