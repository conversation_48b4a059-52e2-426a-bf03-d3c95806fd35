@extends('templates.app')
@section('title', $title)

@push('style')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="{{ asset('assets/css/monitoring-cpr.css') }}">
@endpush

@section('content')
    <!-- Header Section -->
    <header class="d-flex justify-content-between align-items-center mb-4" role="banner">
        <div>
            <h1 class="h4 mb-1 text-dark fw-bold">{{ $title }}</h1>
            <p class="text-muted mb-0">Monitor performa kampanye Facebook Ads secara real-time</p>
        </div>
        <div class="d-flex align-items-center gap-3" role="status" aria-live="polite">
            <div class="auto-refresh-indicator" id="autoRefreshIndicator"
                 aria-label="Status auto-refresh" role="status">
                <span class="refresh-dot" id="refreshDot" aria-hidden="true"></span>
                <span id="refreshStatus">Auto-refresh: ON</span>
            </div>
            <div class="sync-status success" id="syncStatus"
                 aria-label="Status sinkronisasi" role="status">
                <i class="bx bx-check-circle" aria-hidden="true"></i>
                <span>Tersinkronisasi</span>
            </div>
        </div>
    </header>



    <div class="card table-modern">
        <div class="card-body position-relative">
            <!-- Loading Overlay for Table -->
            <div class="loading-overlay d-none" id="tableLoading">
                <div class="loading-spinner"></div>
            </div>

            <!-- Enhanced Filter Section -->
            <section class="filter-section" role="search" aria-label="Filter dan kontrol monitoring">
                <form id="filterForm" role="search">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <label for="idAkun" class="form-label fw-semibold">
                                <i class="bx bx-user me-1" aria-hidden="true"></i>Akun Iklan
                            </label>
                            <select class="form-select" id="idAkun" name="idAkun"
                                    aria-describedby="idAkunHelp">
                                <option value="">Pilih Akun Iklan</option>
                                @foreach ($akun as $a)
                                    <option value="{{ $a['id'] }}">{{ $a['nama'] }} ({{ $a['id'] }})</option>
                                @endforeach
                            </select>
                            <div id="idAkunHelp" class="form-text visually-hidden">
                                Pilih akun iklan untuk memfilter data kampanye
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="dateRangePicker" class="form-label fw-semibold">
                                <i class="bx bx-calendar me-1" aria-hidden="true"></i>Rentang Tanggal
                            </label>
                            <input type="text" id="dateRangePicker" class="form-control"
                                   value="{{ date('Y-m-01') . ' to ' . date('Y-m-t') }}"
                                   placeholder="Pilih Rentang Tanggal"
                                   aria-describedby="dateHelp">
                            <div id="dateHelp" class="form-text visually-hidden">
                                Pilih rentang tanggal untuk memfilter data kampanye
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label for="statusFilter" class="form-label fw-semibold">
                                <i class="bx bx-filter me-1" aria-hidden="true"></i>Status
                            </label>
                            <select class="form-select" id="statusFilter" aria-describedby="statusHelp">
                                <option value="">Semua Status</option>
                                <option value="ACTIVE">Aktif</option>
                                <option value="PAUSED">Dijeda</option>
                                <option value="ARCHIVED">Diarsipkan</option>
                            </select>
                            <div id="statusHelp" class="form-text visually-hidden">
                                Filter kampanye berdasarkan status
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label for="refreshInterval" class="form-label fw-semibold">
                                <i class="bx bx-time me-1" aria-hidden="true"></i>Interval Refresh
                            </label>
                            <select class="form-select" id="refreshInterval" aria-describedby="intervalHelp">
                                <option value="10">10 detik</option>
                                <option value="30" selected>30 detik</option>
                                <option value="60">1 menit</option>
                                <option value="300">5 menit</option>
                            </select>
                            <div id="intervalHelp" class="form-text visually-hidden">
                                Atur interval auto-refresh data
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-semibold">Aksi</label>
                            <div class="d-flex gap-1" role="group" aria-label="Kontrol aksi monitoring">
                                <button id="btnRefreshData" type="button"
                                        class="btn btn-success btn-sm btn-modern"
                                        aria-label="Refresh data secara manual">
                                    <i class="bx bx-refresh" aria-hidden="true"></i>
                                    <span class="visually-hidden">Refresh Data</span>
                                </button>
                                <button id="btnToggleAutoRefresh" type="button"
                                        class="btn btn-outline-primary btn-sm btn-modern"
                                        aria-label="Toggle auto refresh">
                                    <i class="bx bx-time" aria-hidden="true"></i>
                                    <span class="visually-hidden">Toggle Auto Refresh</span>
                                </button>
                                <button id="btnSyncData" type="button"
                                        class="btn btn-primary btn-sm btn-modern"
                                        aria-label="Sinkronisasi data dengan Facebook">
                                    <i class="bx bx-sync" aria-hidden="true"></i>
                                    <span class="visually-hidden">Sinkronisasi Data</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="last-updated">
                            <i class="bx bx-time-five"></i>
                            Terakhir diperbarui: <span id="lastUpdated">-</span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-end gap-2">
                            <span class="small text-muted">Refresh otomatis dalam:</span>
                            <span class="badge bg-primary" id="countdownTimer">30s</span>
                        </div>
                    </div>
                </div>
            </section>

            <div class="table-responsive" role="region" aria-label="Data kampanye monitoring CPR">
                <table id="tabelMasterCampaign" class="table table-hover"
                       role="table" aria-label="Tabel monitoring CPR kampanye Facebook Ads">
                    <thead>
                        <tr role="row">
                            <th scope="col" width="5%" role="columnheader" aria-sort="none">No</th>
                            <th scope="col" width="30%" role="columnheader" aria-sort="none">Kampanye</th>
                            <th scope="col" width="10%" role="columnheader" aria-sort="none">Status</th>
                            <th scope="col" width="15%" role="columnheader" aria-sort="none">Hasil</th>
                            <th scope="col" width="20%" role="columnheader" aria-sort="none">CPR</th>
                            <th scope="col" width="20%" role="columnheader" aria-sort="none">Total Pengeluaran</th>
                        </tr>
                    </thead>
                    <tbody role="rowgroup">
                        <!-- Data will be populated by DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div id="modalContainer"></div>
@endsection

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.js"></script>
    <script src="{{ asset('assets/js/monitoring-cpr.js') }}"></script>
    <script>
        $(document).ready(function() {
            // Configuration object with routes
            const config = {
                routes: {
                    data: "{{ route('ads.monitoring-cpr.data') }}",
                    create: "{{ route('ads.monitoring-cpr.create') }}",
                    store: "{{ route('ads.monitoring-cpr.store') }}",
                    edit: "{{ route('ads.monitoring-cpr.edit', ':id') }}",
                    update: "{{ route('ads.monitoring-cpr.update', ':id') }}"
                }
            };

            // Initialize the monitoring system
            window.monitoringCPR = new MonitoringCPR(config);

            // Legacy event handler for edit button (if needed)
            $('#tabelCampaign').on('click', '.btn-edit', function() {
                var id = $(this).data('id');
                var url = "{{ route('ads.monitoring-cpr.edit', ':id') }}".replace(':id', id);
                $.get(url, function(data) {
                    $('#modalContainer').html(data);
                    $('#modalCreateCampaign').modal('show');

                    $(document).off('click', '#btnSimpanCampaign').on('click', '#btnSimpanCampaign', function() {
                        simpanData(
                            '#formCampaign',
                            '#modalCreateCampaign',
                            "{{ route('ads.monitoring-cpr.update', ':id') }}".replace(':id', id),
                            window.monitoringCPR.table
                        );
                    });
                });
            });
        });
    </script>
@endpush
