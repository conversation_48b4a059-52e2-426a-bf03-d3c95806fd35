/* ===== MONITORING CPR STYLES ===== */

/* Table Styles */
#tabelMasterCampaign {
    text-align: center;
}

/* Action Container */
.action-container {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.25);
}

.stats-item {
    text-align: center;
    position: relative;
}

.stats-number {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-label {
    font-size: 0.95rem;
    opacity: 0.95;
    font-weight: 500;
}

.stats-change {
    font-size: 0.75rem;
    margin-top: 5px;
    opacity: 0.8;
}

.stats-change.positive {
    color: #4ade80;
}

.stats-change.negative {
    color: #f87171;
}

/* Auto Refresh Indicator */
.auto-refresh-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
    border: 1px solid rgba(40, 167, 69, 0.2);
    transition: all 0.3s ease;
}

.auto-refresh-indicator.disabled {
    color: #6c757d;
    background: rgba(108, 117, 125, 0.1);
    border-color: rgba(108, 117, 125, 0.2);
}

.refresh-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #28a745;
    animation: pulse 2s infinite;
}

.refresh-dot.disabled {
    background-color: #6c757d;
    animation: none;
}

/* Animations */
@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Filter Section */
.filter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 25px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.btn-group-toggle .btn {
    margin-right: 5px;
}

/* Status Indicators */
.last-updated {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.sync-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.sync-status.syncing {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.sync-status.success {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.sync-status.error {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

/* Modern Button Styles */
.btn-modern {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 12px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-card {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .stats-number {
        font-size: 1.8rem;
    }
    
    .filter-section {
        padding: 15px;
    }
    
    .action-container {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Accessibility Improvements */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .stats-card {
        border: 2px solid #000;
    }
    
    .sync-status {
        border-width: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .stats-card,
    .btn-modern,
    .auto-refresh-indicator {
        transition: none;
    }
    
    .refresh-dot {
        animation: none;
    }
    
    .loading-spinner {
        animation: none;
    }
}
