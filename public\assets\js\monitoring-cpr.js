/**
 * Monitoring CPR JavaScript Module
 * Handles real-time monitoring of Facebook Ads campaigns
 */

class MonitoringCPR {
    constructor(config = {}) {
        // Configuration with defaults
        this.config = {
            refreshInterval: 30,
            autoRefreshEnabled: true,
            routes: {
                data: config.routes?.data || "",
                create: config.routes?.create || "",
                store: config.routes?.store || "",
                edit: config.routes?.edit || "",
                update: config.routes?.update || "",
            },
            selectors: {
                table: "#tabelMasterCampaign",
                dateRangePicker: "#dateRangePicker",
                idAkun: "#idAkun",
                statusFilter: "#statusFilter",
                refreshInterval: "#refreshInterval",
                btnRefreshData: "#btnRefreshData",
                btnToggleAutoRefresh: "#btnToggleAutoRefresh",
                btnSyncData: "#btnSyncData",
                autoRefreshIndicator: "#autoRefreshIndicator",
                refreshDot: "#refreshDot",
                refreshStatus: "#refreshStatus",
                syncStatus: "#syncStatus",
                lastUpdated: "#lastUpdated",
                countdownTimer: "#countdownTimer",
                tableLoading: "#tableLoading",
                modalContainer: "#modalContainer",
            },
            constants: {
                PESO_TO_RUPIAH_RATE: 278.62,
                CPR_THRESHOLDS: {
                    HIGH: 50000,
                    MEDIUM: 25000,
                },
            },
        };

        // State management
        this.state = {
            autoRefreshEnabled: true,
            autoRefreshInterval: null,
            countdownInterval: null,
            lastUpdateTime: null,
            refreshIntervalSeconds: 30,
            countdownSeconds: 30,
            previousStats: {},
            isFirstLoad: true,
            currentFilters: {
                date: "",
                idAkun: "",
                status: "",
            },
        };

        this.init();
    }

    /**
     * Initialize the monitoring system
     */
    init() {
        this.initializeDatePicker();
        this.initializeDataTable();
        this.bindEvents();
        this.startMonitoring();
    }

    /**
     * Initialize Flatpickr date picker
     */
    initializeDatePicker() {
        flatpickr(this.config.selectors.dateRangePicker, {
            mode: "range",
            dateFormat: "Y-m-d",
            locale: {
                rangeSeparator: " to ",
            },
        });
    }

    /**
     * Initialize DataTable with enhanced configuration
     */
    initializeDataTable() {
        this.table = $(this.config.selectors.table).DataTable({
            ajax: {
                url: this.config.routes.data,
                data: (d) => this.getTableData(d),
                dataSrc: (json) => this.processTableData(json),
                error: (xhr, error, thrown) =>
                    this.handleAjaxError(xhr, error, thrown),
            },
            processing: true,
            serverSide: true,
            pageLength: 25,
            lengthMenu: [
                [10, 25, 50, 100],
                [10, 25, 50, 100],
            ],
            order: [[1, "desc"]],
            language: this.getDataTableLanguage(),
            columns: this.getTableColumns(),
            responsive: true,
            accessibility: {
                enabled: true,
            },
        });
    }

    /**
     * Get data for DataTable AJAX request
     */
    getTableData(d) {
        return {
            ...d,
            date:
                this.state.currentFilters.date ||
                $(this.config.selectors.dateRangePicker).val(),
            idAkun:
                this.state.currentFilters.idAkun ||
                $(this.config.selectors.idAkun).val(),
            status:
                this.state.currentFilters.status ||
                $(this.config.selectors.statusFilter).val(),
        };
    }

    /**
     * Process DataTable response data
     */
    processTableData(json) {
        try {
            this.updateStatistics(json.data);
            this.updateLastUpdatedTime();
            this.updateSyncStatus("success", "Tersinkronisasi");

            if (!this.state.isFirstLoad) {
                this.showNotification(
                    "Data Diperbarui",
                    `${json.data.length} kampanye berhasil dimuat`,
                    "success"
                );
            }

            return json.data;
        } catch (error) {
            console.error("Error processing data:", error);
            this.updateSyncStatus("error", "Gagal memproses data");
            this.showNotification(
                "Error",
                "Gagal memproses data dari server",
                "error"
            );
            return [];
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Handle AJAX errors
     */
    handleAjaxError(xhr, error, thrown) {
        console.error("AJAX Error:", error);
        this.updateSyncStatus("error", "Koneksi bermasalah");
        this.showNotification(
            "Koneksi Error",
            "Gagal mengambil data dari server",
            "error"
        );
        this.hideLoading();
    }

    /**
     * Get DataTable language configuration
     */
    getDataTableLanguage() {
        return {
            processing:
                '<div class="d-flex align-items-center"><div class="loading-spinner me-2"></div>Memuat data...</div>',
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Tidak ada data tersedia",
            infoFiltered: "(difilter dari _MAX_ total data)",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya",
            },
        };
    }

    /**
     * Get table column configuration
     */
    getTableColumns() {
        return [
            {
                data: "DT_RowIndex",
                name: "DT_RowIndex",
                orderable: false,
                searchable: false,
                width: "5%",
                className: "text-center",
            },
            {
                data: "nama_kampanye",
                render: (data) => this.renderCampaignName(data),
                width: "30%",
            },
            {
                data: "status",
                render: (data) => this.renderStatus(data),
                width: "10%",
                className: "text-center",
            },
            {
                data: "hasil",
                render: (data) => this.renderResults(data),
                width: "15%",
                className: "text-center",
            },
            {
                data: "cpr",
                render: (data) => this.renderCPR(data),
                width: "20%",
                className: "text-center",
            },
            {
                data: "biaya_dibelanjakan",
                render: (data) => this.renderSpend(data),
                width: "20%",
                className: "text-center",
            },
        ];
    }

    /**
     * Render campaign name with truncation
     */
    renderCampaignName(data) {
        if (!data) return "-";
        const truncated = this.limitText(data, 40);
        return `<div style="text-align:left;" title="${data}">
                    <strong>${truncated}</strong>
                </div>`;
    }

    /**
     * Render status badge
     */
    renderStatus(data) {
        if (!data) return "-";

        const statusConfig = {
            ACTIVE: { class: "success", icon: "bx-play-circle" },
            PAUSED: { class: "warning", icon: "bx-pause-circle" },
            ARCHIVED: { class: "danger", icon: "bx-archive" },
        };

        const config = statusConfig[data] || {
            class: "secondary",
            icon: "bx-help-circle",
        };

        return `<span class="badge bg-${config.class}" role="status" aria-label="Status ${data}">
                    <i class="bx ${config.icon} me-1" aria-hidden="true"></i>${data}
                </span>`;
    }

    /**
     * Render results with formatting
     */
    renderResults(data) {
        const formatted = this.formatNumber(data);
        return `<span class="fw-semibold">${formatted}</span>`;
    }

    /**
     * Render CPR with color coding
     */
    renderCPR(data) {
        const formatted = this.formatCurrency(data);
        const value = parseFloat(data);
        let colorClass = "text-dark";
        let icon = "";

        if (value > this.config.constants.CPR_THRESHOLDS.HIGH) {
            colorClass = "text-danger";
            icon = '<i class="bx bx-trending-up me-1" aria-hidden="true"></i>';
        } else if (value > this.config.constants.CPR_THRESHOLDS.MEDIUM) {
            colorClass = "text-warning";
            icon = '<i class="bx bx-minus me-1" aria-hidden="true"></i>';
        } else if (value > 0) {
            colorClass = "text-success";
            icon =
                '<i class="bx bx-trending-down me-1" aria-hidden="true"></i>';
        }

        return `<span class="${colorClass} fw-semibold" role="cell" aria-label="CPR ${formatted}">
                    ${icon}${formatted}
                </span>`;
    }

    /**
     * Render spend amount
     */
    renderSpend(data) {
        const formatted = this.formatCurrency(data);
        return `<span class="fw-semibold">${formatted}</span>`;
    }

    /**
     * Bind all event handlers
     */
    bindEvents() {
        // Filter events
        $(this.config.selectors.dateRangePicker).on("change", () =>
            this.handleDateChange()
        );
        $(this.config.selectors.idAkun).on("change", () =>
            this.handleAccountChange()
        );
        $(this.config.selectors.statusFilter).on("change", () =>
            this.handleStatusChange()
        );
        $(this.config.selectors.refreshInterval).on("change", () =>
            this.handleIntervalChange()
        );

        // Button events
        $(this.config.selectors.btnRefreshData).on("click", () =>
            this.handleManualRefresh()
        );
        $(this.config.selectors.btnToggleAutoRefresh).on("click", () =>
            this.toggleAutoRefresh()
        );
        $(this.config.selectors.btnSyncData).on("click", () =>
            this.handleSyncData()
        );

        // Cleanup on page unload
        $(window).on("beforeunload", () => this.cleanup());
    }

    /**
     * Handle date range change
     */
    handleDateChange() {
        const dateValue = $(this.config.selectors.dateRangePicker).val();
        if (dateValue.split(" to ").length === 2) {
            this.state.currentFilters.date = dateValue;
            this.showLoading("table");
            this.updateSyncStatus("syncing", "Memfilter data...");
            this.refreshData();
        }
    }

    /**
     * Handle account selection change
     */
    handleAccountChange() {
        const idAkun = $(this.config.selectors.idAkun).val();
        const selectedText = $(this.config.selectors.idAkun)
            .find("option:selected")
            .text();

        this.state.currentFilters.idAkun = idAkun;
        this.showLoading("table");
        this.updateSyncStatus("syncing", "Mengubah akun...");
        this.refreshData();

        if (idAkun) {
            this.showNotification(
                "Akun Diubah",
                `Menampilkan data untuk: ${selectedText}`,
                "success"
            );
        }
    }

    /**
     * Handle status filter change
     */
    handleStatusChange() {
        const statusFilter = $(this.config.selectors.statusFilter).val();
        const statusText = $(this.config.selectors.statusFilter)
            .find("option:selected")
            .text();

        this.state.currentFilters.status = statusFilter;
        this.showLoading("table");
        this.updateSyncStatus("syncing", "Memfilter status...");
        this.refreshData();

        this.showNotification(
            "Filter Status",
            `Menampilkan kampanye: ${statusText}`,
            "success"
        );
    }

    /**
     * Handle refresh interval change
     */
    handleIntervalChange() {
        const newInterval = $(this.config.selectors.refreshInterval).val();
        this.updateRefreshInterval(newInterval);
    }

    /**
     * Handle manual refresh button click
     */
    handleManualRefresh() {
        this.showLoading("buttons");
        this.refreshData();
        this.showNotification(
            "Refresh Manual",
            "Data sedang diperbarui...",
            "success"
        );
    }

    /**
     * Handle sync data button click
     */
    handleSyncData() {
        const selectedIdAkun = $(this.config.selectors.idAkun).val();
        const namaAkun = $(this.config.selectors.idAkun)
            .find("option:selected")
            .text();

        if (!selectedIdAkun) {
            this.showNotification(
                "Pilih Akun",
                "Silakan pilih akun iklan terlebih dahulu untuk sinkronisasi",
                "warning"
            );
            return;
        }

        this.showLoading("buttons");
        this.updateSyncStatus("syncing", "Sinkronisasi dengan Facebook...");

        $.get(this.config.routes.create, {
            idAkun: selectedIdAkun,
            namaAkun: namaAkun,
        })
            .done((data) => this.handleSyncSuccess(data, namaAkun))
            .fail((xhr) => this.handleSyncError(xhr));
    }

    /**
     * Handle successful sync response
     */
    handleSyncSuccess(data, namaAkun) {
        $(this.config.selectors.modalContainer).html(data);
        $("#modalCreateCampaign").modal("show");
        this.hideLoading();
        this.updateSyncStatus("success", "Tersinkronisasi");

        this.showNotification(
            "Sinkronisasi Berhasil",
            `Data kampanye dari ${namaAkun} berhasil dimuat`,
            "success"
        );

        // Bind save button event
        $(document)
            .off("click", "#btnSimpanCampaign")
            .on("click", "#btnSimpanCampaign", () => {
                this.saveData(
                    "#formCampaign",
                    "#modalCreateCampaign",
                    this.config.routes.store
                );
            });
    }

    /**
     * Handle sync error
     */
    handleSyncError(xhr) {
        this.hideLoading();
        this.updateSyncStatus("error", "Gagal sinkronisasi");

        let errorMessage = "Gagal mengambil data dari Facebook Ads";
        if (xhr.responseJSON?.message) {
            errorMessage = xhr.responseJSON.message;
        }

        this.showNotification("Sinkronisasi Gagal", errorMessage, "error");
    }

    /**
     * Refresh data and reset countdown
     */
    refreshData() {
        this.showLoading("table");
        this.updateSyncStatus("syncing", "Memperbarui data...");
        this.table.draw();
        this.resetCountdown();
    }

    /**
     * Start monitoring system
     */
    startMonitoring() {
        this.updateCountdownDisplay();
        this.startAutoRefresh();
        this.updateLastUpdatedTime();

        // Show welcome notification after a delay
        setTimeout(() => {
            this.showNotification(
                "Monitoring CPR Aktif",
                `Auto-refresh setiap ${this.state.refreshIntervalSeconds} detik. Selamat memantau!`,
                "success"
            );
        }, 1000);
    }

    /**
     * Start auto-refresh functionality
     */
    startAutoRefresh() {
        if (this.state.autoRefreshInterval) {
            clearInterval(this.state.autoRefreshInterval);
        }
        this.startCountdown();
    }

    /**
     * Stop auto-refresh functionality
     */
    stopAutoRefresh() {
        if (this.state.autoRefreshInterval) {
            clearInterval(this.state.autoRefreshInterval);
            this.state.autoRefreshInterval = null;
        }
        if (this.state.countdownInterval) {
            clearInterval(this.state.countdownInterval);
            this.state.countdownInterval = null;
        }
    }

    /**
     * Toggle auto-refresh on/off
     */
    toggleAutoRefresh() {
        this.state.autoRefreshEnabled = !this.state.autoRefreshEnabled;

        const indicator = $(this.config.selectors.autoRefreshIndicator);
        const dot = $(this.config.selectors.refreshDot);
        const status = $(this.config.selectors.refreshStatus);
        const countdownTimer = $(this.config.selectors.countdownTimer);

        if (this.state.autoRefreshEnabled) {
            indicator.removeClass("disabled");
            dot.removeClass("disabled");
            status.text("Auto-refresh: ON");
            countdownTimer.removeClass("d-none");
            this.resetCountdown();
            this.startAutoRefresh();

            this.showNotification(
                "Auto-refresh Diaktifkan",
                `Data akan diperbarui setiap ${this.state.refreshIntervalSeconds} detik`,
                "success"
            );
        } else {
            indicator.addClass("disabled");
            dot.addClass("disabled");
            status.text("Auto-refresh: OFF");
            countdownTimer.addClass("d-none");
            this.stopAutoRefresh();

            this.showNotification(
                "Auto-refresh Dinonaktifkan",
                "Refresh otomatis telah dihentikan",
                "warning"
            );
        }
    }

    /**
     * Update refresh interval
     */
    updateRefreshInterval(newInterval) {
        this.state.refreshIntervalSeconds = parseInt(newInterval);
        this.state.countdownSeconds = this.state.refreshIntervalSeconds;

        if (this.state.autoRefreshEnabled) {
            this.stopAutoRefresh();
            this.startAutoRefresh();

            this.showNotification(
                "Interval Diperbarui",
                `Auto-refresh sekarang setiap ${this.state.refreshIntervalSeconds} detik`,
                "success"
            );
        }

        this.updateCountdownDisplay();
    }

    /**
     * Start countdown timer
     */
    startCountdown() {
        if (this.state.countdownInterval) {
            clearInterval(this.state.countdownInterval);
        }

        this.state.countdownInterval = setInterval(() => {
            if (
                this.state.autoRefreshEnabled &&
                this.state.countdownSeconds > 0
            ) {
                this.state.countdownSeconds--;
                this.updateCountdownDisplay();
            } else if (
                this.state.autoRefreshEnabled &&
                this.state.countdownSeconds <= 0
            ) {
                this.state.countdownSeconds = this.state.refreshIntervalSeconds;
                this.refreshData();
            }
        }, 1000);
    }

    /**
     * Update countdown display
     */
    updateCountdownDisplay() {
        const minutes = Math.floor(this.state.countdownSeconds / 60);
        const seconds = this.state.countdownSeconds % 60;
        const display = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
        $(this.config.selectors.countdownTimer).text(display);
    }

    /**
     * Reset countdown timer
     */
    resetCountdown() {
        this.state.countdownSeconds = this.state.refreshIntervalSeconds;
        this.updateCountdownDisplay();
    }

    /**
     * Update statistics display
     */
    updateStatistics(data) {
        const totalCampaigns = data.length;
        const activeCampaigns = data.filter(
            (item) => item.status === "ACTIVE"
        ).length;
        const totalSpend = data.reduce(
            (sum, item) => sum + parseFloat(item.biaya_dibelanjakan || 0),
            0
        );
        const totalResults = data.reduce(
            (sum, item) => sum + parseInt(item.hasil || 0),
            0
        );
        const avgCpr = totalResults > 0 ? totalSpend / totalResults : 0;

        const currentStats = {
            totalCampaigns,
            activeCampaigns,
            totalSpend,
            avgCpr,
        };

        // Update main numbers with animation
        this.animateNumber(
            "#totalCampaigns",
            this.formatNumber(totalCampaigns)
        );
        this.animateNumber(
            "#activeCampaigns",
            this.formatNumber(activeCampaigns)
        );
        this.animateNumber("#totalSpend", this.formatCurrency(totalSpend));
        this.animateNumber("#avgCpr", this.formatCurrency(avgCpr));

        // Show changes if not first load
        if (
            !this.state.isFirstLoad &&
            Object.keys(this.state.previousStats).length > 0
        ) {
            this.updateStatChange(
                "#totalCampaignsChange",
                currentStats.totalCampaigns,
                this.state.previousStats.totalCampaigns
            );
            this.updateStatChange(
                "#activeCampaignsChange",
                currentStats.activeCampaigns,
                this.state.previousStats.activeCampaigns
            );
            this.updateStatChange(
                "#totalSpendChange",
                currentStats.totalSpend,
                this.state.previousStats.totalSpend,
                true
            );
            this.updateStatChange(
                "#avgCprChange",
                currentStats.avgCpr,
                this.state.previousStats.avgCpr,
                true
            );
        }

        this.state.previousStats = currentStats;
        this.state.isFirstLoad = false;
    }

    /**
     * Animate number changes
     */
    animateNumber(selector, newValue) {
        const element = $(selector);
        if (element.length) {
            element.fadeOut(200, function () {
                element.text(newValue).fadeIn(200);
            });
        }
    }

    /**
     * Update stat change indicators
     */
    updateStatChange(selector, current, previous, isCurrency = false) {
        const element = $(selector);
        if (!element.length) return;

        const change = current - previous;
        const percentage =
            previous !== 0 ? ((change / previous) * 100).toFixed(1) : 0;

        if (change === 0) {
            element.text("").removeClass("positive negative");
            return;
        }

        const isPositive = change > 0;
        const changeText = isCurrency
            ? `${isPositive ? "+" : ""}${this.formatCurrency(
                  Math.abs(change)
              )} (${isPositive ? "+" : ""}${percentage}%)`
            : `${isPositive ? "+" : ""}${this.formatNumber(
                  Math.abs(change)
              )} (${isPositive ? "+" : ""}${percentage}%)`;

        element
            .text(changeText)
            .removeClass("positive negative")
            .addClass(isPositive ? "positive" : "negative");
    }

    /**
     * Update last updated time
     */
    updateLastUpdatedTime() {
        this.state.lastUpdateTime = new Date();
        $(this.config.selectors.lastUpdated).text(
            this.state.lastUpdateTime.toLocaleString("id-ID", {
                year: "numeric",
                month: "short",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
                second: "2-digit",
            })
        );
    }

    /**
     * Show loading indicators
     */
    showLoading(target = "buttons") {
        if (target === "buttons") {
            $("#btnRefreshData i")
                .removeClass("bx-refresh")
                .addClass("bx-loader-alt bx-spin");
            $("#btnSyncData i")
                .removeClass("bx-sync")
                .addClass("bx-loader-alt bx-spin");
        } else if (target === "table") {
            $(this.config.selectors.tableLoading).removeClass("d-none");
        }
    }

    /**
     * Hide loading indicators
     */
    hideLoading() {
        $("#btnRefreshData i")
            .removeClass("bx-loader-alt bx-spin")
            .addClass("bx-refresh");
        $("#btnSyncData i")
            .removeClass("bx-loader-alt bx-spin")
            .addClass("bx-sync");
        $(this.config.selectors.tableLoading).addClass("d-none");
    }

    /**
     * Update sync status indicator
     */
    updateSyncStatus(status, message) {
        const syncStatus = $(this.config.selectors.syncStatus);
        const icon = syncStatus.find("i");
        const text = syncStatus.find("span");

        // Reset classes
        syncStatus.removeClass("syncing success error");
        icon.removeClass(
            "bx-loader-alt bx-spin bx-check-circle bx-error-circle"
        );

        // Set new status
        syncStatus.addClass(status);
        text.text(message);

        switch (status) {
            case "syncing":
                icon.addClass("bx-loader-alt bx-spin");
                break;
            case "success":
                icon.addClass("bx-check-circle");
                break;
            case "error":
                icon.addClass("bx-error-circle");
                break;
        }
    }

    /**
     * Show notification using SweetAlert2 (as per user preference)
     */
    showNotification(title, message, type = "success") {
        // Map type to SweetAlert2 icon
        let icon = "success";
        switch (type) {
            case "error":
                icon = "error";
                break;
            case "warning":
                icon = "warning";
                break;
            case "info":
                icon = "info";
                break;
            default:
                icon = "success";
        }

        // Create SweetAlert2 toast (as per user preference)
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 4000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener("mouseenter", Swal.stopTimer);
                toast.addEventListener("mouseleave", Swal.resumeTimer);
            },
        });

        Toast.fire({
            icon: icon,
            title: title,
            text: message,
        });
    }

    /**
     * Utility: Limit text length
     */
    limitText(text, limit = 25) {
        if (!text) return "-";
        return text.length > limit ? text.substring(0, limit) + "..." : text;
    }

    /**
     * Utility: Format currency (Peso to Rupiah)
     */
    formatCurrency(value) {
        if (!value || value === 0) return "-";
        const pesoValue = parseFloat(value);
        const rupiah = pesoValue * this.config.constants.PESO_TO_RUPIAH_RATE;
        return (
            "Rp " +
            rupiah.toLocaleString("id-ID", {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
            })
        );
    }

    /**
     * Utility: Format number
     */
    formatNumber(value) {
        if (!value || value === 0) return "0";
        return parseInt(value).toLocaleString("id-ID");
    }

    /**
     * Save data (placeholder for form submission)
     */
    saveData(formSelector, modalSelector, url) {
        // This method would handle form submission
        // Implementation depends on the specific form structure
        console.log("Save data method called", {
            formSelector,
            modalSelector,
            url,
        });
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        this.stopAutoRefresh();
        if (this.table) {
            this.table.destroy();
        }
    }
}

// Export for use in other files
window.MonitoringCPR = MonitoringCPR;
